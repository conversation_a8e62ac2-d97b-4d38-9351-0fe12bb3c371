import { SettingOutlined } from "@ant-design/icons"
import { Dropdown } from "antd"
import { useDispatch, useSelector } from "react-redux"
import { removeAllTag, removeOtherTag, removeTagByPath } from "~/redux/slices/tagSlice"
import { useTranslation } from "react-i18next"
import { RootState } from "~/redux/store"

function TagsViewAction() {
  const { activeTagId } = useSelector((state: RootState) => state.tag)
  const dispatch = useDispatch()
  const { t } = useTranslation()

  return (
    <Dropdown
      menu={{
        items: [
          {
            key: "0",
            onClick: () => {
              dispatch(removeTagByPath(activeTagId))
            },
            label: t("tagsView.closeCurrent"),
          },
          {
            key: "1",
            onClick: () => {
              dispatch(removeOtherTag(undefined))
            },
            label: t("tagsView.closeOther"),
          },
          {
            key: "2",
            onClick: () => {
              dispatch(removeAllTag(undefined))
            },
            label: t("tagsView.closeAll"),
          },
        ],
      }}
    >
        <SettingOutlined className="mr-2" />
    </Dropdown>
  )
}

export default TagsViewAction
