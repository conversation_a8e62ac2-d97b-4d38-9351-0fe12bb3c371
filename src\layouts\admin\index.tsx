import React from "react";
import { Navigate, Outlet } from "react-router"
import { useAppAuth } from "~/hooks";
import { PATH_GUEST } from "~/constants";
import Dashboard from "./Dashboard"

export const AdminLayout = ({children}: {children?: React.ReactNode}) => {
  const { isAuthenticated } = useAppAuth()
  if (!isAuthenticated) {
    return <Navigate to={PATH_GUEST.root} replace />;
  }

  return (
    <Dashboard>
      {children ?? <Outlet />}
    </Dashboard>
  )
}
