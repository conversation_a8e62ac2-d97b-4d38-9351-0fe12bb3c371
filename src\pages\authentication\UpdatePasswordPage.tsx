import { CheckCircleOutlined, DownOutlined, EyeInvisibleOutlined, EyeTwoTone, LockOutlined } from "@ant-design/icons"
import { ProCard, ProFormText } from "@ant-design/pro-components"
import { <PERSON>ert, Button, Dropdown, Flex, Form, Image, Result, Space, Typography } from "antd"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Link, Navigate, useLocation } from "react-router"
import EnUSSvg from "~/assets/en_US.svg?url"
import FrFRSvg from "~/assets/fr_FR.svg"
import { LogoDouane } from "~/components"
import { PATH_AUTH, PATH_GUEST } from "~/constants"
import { useAppLocal, useAppTheme } from "~/hooks"
import { themeIcon } from "~/services/utils"
import { createStyles } from "~/themes"

const { Title, Text } = Typography

interface UpdatePasswordFormData {
  newPassword: string;
  confirmPassword: string;
}

const initialValues: UpdatePasswordFormData = {
  newPassword: "",
  confirmPassword: "",
}

const UpdatePasswordPage = () => {
  const { theme, updateTheme } = useAppTheme()
  const { local, setLocal } = useAppLocal()
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [passwordUpdated, setPasswordUpdated] = useState(false)
  const { styles } = useStyles()
  const location = useLocation()
  const [email, ] = useState(location.state?.email)

  if (!email) return <Navigate to={PATH_AUTH.root} replace />;

  const onFinish = async (values: UpdatePasswordFormData) => {
    setLoading(true)
    try {
      // Simuler la mise à jour du mot de passe (dans un vrai projet, appel API ici)
      await new Promise(resolve => setTimeout(resolve, 2000))

      setPasswordUpdated(true)
    } catch (error) {
      console.error('Erreur lors de la mise à jour du mot de passe:', error)
    } finally {
      setLoading(false)
    }
  }

  const toggleTheme = () => {
    const newTheme = theme === "dark" ? "light" : "dark";
    updateTheme(newTheme);
  }

  // Affichage de la confirmation de mise à jour
  if (passwordUpdated) {
    return (
      <ProCard layout="center" className="min-h-screen">
        <Flex vertical align="center" justify="center" className="text-center my-12">
          <div className="flex justify-between w-full mb-2">
            <Button
              shape="round"
              color="default"
              variant="filled"
              icon={themeIcon(theme)}
              onClick={() => toggleTheme()}
            />
            <Dropdown
              menu={{
                onClick: (info: any) => setLocal(info.key),
                items: [
                  {
                    key: "en",
                    icon: <Image preview={false} src={EnUSSvg} alt="en" />,
                    disabled: local === "en",
                    label: <span className="ml-1">{t("English")}</span>,
                  },
                  {
                    key: "fr",
                    icon: <Image preview={false} src={FrFRSvg} alt="fr" />,
                    disabled: local === "fr",
                    label: <span className="ml-1">{t("French")}</span>,
                  },
                ],
              }}
            >
              <Button
                shape="round"
                color="default"
                variant="filled"
                className="font-bold"
                onClick={e => e.preventDefault()}
              >
                <Space>
                  {local}
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
          </div>

          <Result
            icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            title="Mot de passe mis à jour avec succès !"
            subTitle={
              <div className={styles.successMessage}>
                <Text>
                  Votre mot de passe a été mis à jour avec succès.
                </Text>
                <Text type="secondary" className={styles.instructionText}>
                  Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.
                </Text>
              </div>
            }
            extra={[
              <Link to={PATH_GUEST.root} key="home">
                <Button
                  type="default"
                  size="large"
                  className={styles.actionButton}
                >
                  Retour à l'accueil
                </Button>
              </Link>,
              <Link to={PATH_AUTH.signin} key="signin">
                <Button
                  type="primary"
                  size="large"
                  className={styles.actionButton}
                >
                  Se connecter
                </Button>
              </Link>
            ]}
          />

          <footer className="text-center font-bold mt-8">{t("global.footer")}</footer>
        </Flex>
      </ProCard>
    )
  }

  return (
    <ProCard layout="center" className="min-h-screen">
      <Flex vertical align="center" justify="center" className={"text-center my-12"}>
        <div className="flex justify-between w-full mb-2">
          <Button
            shape="round"
            color="default"
            variant="filled"
            icon={themeIcon(theme)}
            onClick={() => toggleTheme()}
          />
          <Dropdown
            menu={{
              onClick: (info: any) => setLocal(info.key),
              items: [
                {
                  key: "en",
                  icon: <Image preview={false} src={EnUSSvg} alt="en" />,
                  disabled: local === "en",
                  label: <span className="ml-1">{t("English")}</span>,
                },
                {
                  key: "fr",
                  icon: <Image preview={false} src={FrFRSvg} alt="fr" />,
                  disabled: local === "fr",
                  label: <span className="ml-1">{t("French")}</span>,
                },
              ],
            }}
          >
            <Button
              shape="round"
              color="default"
              variant="filled"
              className="font-bold"
              onClick={e => e.preventDefault()}
            >
              <Space>
                {local}
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </div>
        <LogoDouane />
        <Title level={3} className="my-5 uppercase">
          {t("Mise à jour du mot de passe")}
        </Title>

        <Alert
          message="Nouveau mot de passe"
          description="Saisissez votre nouveau mot de passe et confirmez-le pour finaliser la mise à jour."
          type="info"
          showIcon
          className={styles.infoAlert}
        />

        <Form<UpdatePasswordFormData>
          onFinish={onFinish}
          className="w-[350px]"
          initialValues={initialValues}
        >
          <ProFormText.Password
            name="newPassword"
            fieldProps={{
              size: "large",
              prefix: <LockOutlined className="prefixIcon" />,
              iconRender: (visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />),
            }}
            placeholder="Nouveau mot de passe"
            rules={[
              {
                required: true,
                message: t("Le nouveau mot de passe est requis"),
              },
              {
                min: 8,
                message: t("Le mot de passe doit contenir au moins 8 caractères"),
              },
              {
                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                message: t("Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial"),
              },
            ]}
          />

          <ProFormText.Password
            name="confirmPassword"
            fieldProps={{
              size: "large",
              prefix: <LockOutlined className="prefixIcon" />,
              iconRender: (visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />),
            }}
            placeholder="Confirmer le nouveau mot de passe"
            dependencies={['newPassword']}
            rules={[
              {
                required: true,
                message: t("Veuillez confirmer votre nouveau mot de passe"),
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t("Les mots de passe ne correspondent pas")));
                },
              }),
            ]}
          />

          <Form.Item className="mt-4">
            <Button
              size="large"
              htmlType="submit"
              type="primary"
              className="w-full"
              loading={loading}
              icon={<LockOutlined />}
            >
              {loading ? t("Mise à jour en cours...") : t("Mettre à jour le mot de passe")}
            </Button>
          </Form.Item>
        </Form>
        <footer className="text-center font-bold">{t("global.footer")}</footer>
      </Flex>
    </ProCard>
  )
}

const useStyles = createStyles(({ token }) => ({
  successMessage: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    textAlign: 'center',
  },
  instructionText: {
    fontSize: '14px',
    marginTop: '8px',
  },
  actionButton: {
    minWidth: '160px',
    margin: '0 8px',
  },
  infoAlert: {
    marginBottom: '24px',
    maxWidth: '400px',
    textAlign: 'left',
  },
  linkButton: {
    color: token.colorPrimary,
    fontWeight: 500,
    '&:hover': {
      color: token.colorPrimaryHover,
    },
  },
}))

export default UpdatePasswordPage
