import { Prime, PrimeListResponse, PrimeTypeMock, PrimeUploadResponse } from '../../models/Prime';
import request from '../request';

const URL = '/primes';

export const getPrimes = async (): Promise<PrimeListResponse> => {
  try {
    const response = await request.get(URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching primes:', error);
    throw error;
  }
};

export const getPrimeById = async (id: string): Promise<Prime> => {
  try {
    const response = await request.get(`${URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching prime with id ${id}:`, error);
    throw error;
  }
};

export const createPrime = async (prime: Omit<Prime, 'id'>): Promise<Prime> => {
  try {
    const response = await request.post(URL, { data: prime });
    return response;
  } catch (error) {
    console.error('Error creating prime:', error);
    throw error;
  }
};

export const updatePrime = async (id: string, prime: Partial<Prime>): Promise<Prime> => {
  try {
    const response = await request.put(`${URL}/${id}`, { data: prime });
    return response.data;
  } catch (error) {
    console.error(`Error updating prime with id ${id}:`, error);
    throw error;
  }
};

export const deletePrime = async (id: string): Promise<void> => {
  try {
    await request.delete(`${URL}/${id}`);
  } catch (error) {
    console.error(`Error deleting prime with id ${id}:`, error);
    throw error;
  }
};

export const uploadPrimeExcel = async (file: File, type: string): Promise<PrimeUploadResponse> => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const response = await request.post(`${URL}/upload`, { data: formData });

    return response.data;
  } catch (error) {
    console.error('Error uploading prime excel:', error);
    throw error;
  }
};

export const getPrimeTypes = async (): Promise<PrimeTypeMock[]> => {
  try {
    // Dans un environnement réel, on ferait un appel API
    const response = await request.get(`${URL}/types`);
    return Promise.resolve(response);

    // Pour le moment, on utilise les données mockées
    // return Promise.resolve(primeTypes);
  } catch (error) {
    console.error('Error fetching prime types:', error);
    throw error;
  }
};

export const getPrimeTypeByCode = async (code: string): Promise<PrimeTypeMock | undefined> => {
  try {
    const types = await getPrimeTypes();
    return types.find(type => type.code === code);
  } catch (error) {
    console.error(`Error fetching prime type with code ${code}:`, error);
    throw error;
  }
};
