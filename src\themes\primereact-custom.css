/* PrimeReact Custom Styles */

/* DataTable */
.p-datatable {
  font-family: var(--font-family);
  color: var(--text-color);
}

.p-datatable .p-datatable-header {
  background-color: transparent;
  border: none;
  padding: 16px 24px;
  font-weight: 600;
}

.p-datatable .p-datatable-thead > tr > th {
  background-color: var(--surface-b);
  color: var(--text-color);
  font-weight: 600;
  padding: 16px 16px;
  border-width: 0 0 1px 0;
  border-color: var(--input-border);
  transition: background-color 0.3s;
  text-align: left;
}

.p-datatable .p-datatable-tbody > tr {
  background-color: var(--surface-a);
  transition: background-color 0.3s;
}

.p-datatable .p-datatable-tbody > tr > td {
  padding: 16px 16px;
  border-width: 0 0 1px 0;
  border-color: var(--input-border);
}

.p-datatable .p-datatable-tbody > tr:hover {
  background-color: var(--surface-hover);
}

.p-datatable .p-datatable-tbody > tr:focus {
  outline: none;
}

.p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even) {
  background-color: var(--surface-b);
}

.p-datatable .p-paginator {
  background-color: transparent;
  border: none;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.p-datatable .p-paginator .p-paginator-current {
  margin-right: 16px;
}

.p-datatable .p-paginator .p-paginator-page {
  min-width: 32px;
  height: 32px;
  margin: 0 4px;
  border-radius: 6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.p-datatable .p-paginator .p-paginator-page.p-highlight {
  background-color: var(--primary-color);
  color: var(--primary-color-text);
}

.p-datatable .p-paginator .p-paginator-first,
.p-datatable .p-paginator .p-paginator-prev,
.p-datatable .p-paginator .p-paginator-next,
.p-datatable .p-paginator .p-paginator-last {
  min-width: 32px;
  height: 32px;
  border-radius: 6px;
}

.p-datatable .p-datatable-footer {
  background-color: transparent;
  color: var(--text-color-secondary);
  border-top: 1px solid var(--input-border);
  padding: 16px 24px;
  font-weight: normal;
  text-align: right;
}

/* Buttons */
.p-button {
  font-family: var(--font-family);
  border-radius: 6px;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
}

.p-button:focus {
  box-shadow: 0 0 0 2px var(--surface-a), 0 0 0 4px var(--primary-color);
}

.p-button.p-button-text {
  background-color: transparent;
  color: var(--primary-color);
  border: none;
}

.p-button.p-button-text:hover {
  background-color: var(--surface-hover);
  color: var(--primary-color);
}

.p-button.p-button-rounded {
  border-radius: 50%;
}

/* Inputs */
.p-inputtext {
  font-family: var(--font-family);
  padding: 4px 11px;
  border-radius: 6px;
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  color: var(--text-color);
  transition: all 0.3s;
}

.p-inputtext:hover {
  border-color: var(--input-border-hover);
}

.p-inputtext:enabled:focus {
  outline: 0;
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 2px var(--focus-ring);
}

.p-inputtext::placeholder {
  color: var(--input-placeholder);
}

.p-input-icon-left > .p-inputtext {
  padding-left: 2.5rem;
}

.p-input-icon-left > i {
  left: 0.75rem;
  color: var(--text-color-secondary);
}

.p-inputgroup {
  display: flex;
}

.p-inputgroup > .p-inputtext {
  flex: 1 1 auto;
}

.p-inputgroup-addon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 11px;
  background-color: var(--surface-c);
  border: 1px solid var(--input-border);
  color: var(--text-color-secondary);
}

.p-inputgroup-addon:first-child {
  border-right: 0;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.p-inputgroup-addon:last-child {
  border-left: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.p-inputgroup > .p-inputtext:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.p-inputgroup > .p-inputtext:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.p-inputgroup .p-inputgroup-addon.p-clickable {
  cursor: pointer;
}

/* Card */
.card {
  background-color: var(--surface-a);
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Tooltip */
.p-tooltip .p-tooltip-text {
  background-color: var(--surface-a);
  color: var(--text-color);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
}

.p-tooltip.p-tooltip-right .p-tooltip-arrow {
  border-right-color: var(--surface-a);
}

.p-tooltip.p-tooltip-left .p-tooltip-arrow {
  border-left-color: var(--surface-a);
}

.p-tooltip.p-tooltip-top .p-tooltip-arrow {
  border-top-color: var(--surface-a);
}

.p-tooltip.p-tooltip-bottom .p-tooltip-arrow {
  border-bottom-color: var(--surface-a);
}
