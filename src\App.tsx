import { HTMLProps, ReactNode, useEffect, useLayoutEffect, useRef } from "react"
import { App as Feedback<PERSON>rovider, ConfigProvider } from "antd"
import { useAppLocal, useAppTheme } from "./hooks"
import { RouterProvider } from "react-router"
import { Provider as StoreProvider } from "react-redux"
import { store } from "./redux/store"
import { createStyles, getCustomTokenConfig, getThemeConfig, ThemeProvider, GlobalStyles } from "./themes"
import router from "./routes"
import enUS from "antd/lib/locale/en_US"
import frFR from "antd/lib/locale/fr_FR"
import { PrimeReactProvider } from 'primereact/api'

function App() {
  return (
    <StoreProvider store={store}>
      <AppProvider>
        <RouterProvider router={router} />
      </AppProvider>
    </StoreProvider>
  )
}

export default App

type AppProviderProps = {
  children: ReactNode
} & HTMLProps<HTMLDivElement>

function AppProvider({ children }: AppProviderProps) {
  const { local: appLocal } = useAppLocal()
  const { theme: appTheme, updateTheme } = useAppTheme()
  const { styles } = useFileStyles()
  const className = styles.linearGradientButton
  const ref = useRef({updateTheme})

  useEffect(() => {
    // watch system theme change
    const autoUpdateTheme = (e: any) => ref.current.updateTheme(e.matches)
    const mql = window?.matchMedia("(prefers-color-scheme: dark)")
    mql.addEventListener("change", autoUpdateTheme)

    return () => {
      mql.removeEventListener("change", autoUpdateTheme)
    }
  }, [])

  useLayoutEffect(() => {
    ConfigProvider.config({
      holderRender: children => (
        <ThemeProvider defaultThemeMode={appTheme} theme={getThemeConfig} customToken={getCustomTokenConfig}>
            <ConfigProvider
              componentSize="middle"
              locale={appLocal === "fr" ? frFR : enUS}
              button={{ className }}
              >
              <PrimeReactProvider>
                <GlobalStyles />
                <FeedbackProvider message={{ maxCount: 2 }} notification={{ maxCount: 1 }}>
                  {children}
                </FeedbackProvider>
              </PrimeReactProvider>
            </ConfigProvider>
          </ThemeProvider>
      ),
    })
  }, [appTheme, appLocal, className])

  return (
    <ThemeProvider defaultThemeMode={appTheme} theme={getThemeConfig} customToken={getCustomTokenConfig}>
      <ConfigProvider componentSize="middle" locale={appLocal === "fr" ? frFR : enUS} button={{ className }}>
        <PrimeReactProvider>
          <GlobalStyles />
          {children}
        </PrimeReactProvider>
      </ConfigProvider>
    </ThemeProvider>
  )
}

const useFileStyles = createStyles(({ prefixCls, css }) => ({
  linearGradientButton: css`
    &.${prefixCls}-btn-primary:not([disabled]):not(.${prefixCls}-btn-dangerous) {
      > span {
        position: relative;
      }

      &::before {
        content: "";
        background: linear-gradient(135deg, #6253e1, #04befe);
        position: absolute;
        inset: -1px;
        opacity: 1;
        transition: all 0.3s;
        border-radius: inherit;
      }

      &:hover::before {
        opacity: 0;
      }
    }
  `,
}))
