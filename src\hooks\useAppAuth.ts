import { useCallback, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { LoginPayload } from '~/models';
import { doLogin, doLogout } from '~/redux/slices/authSlice';
import { RootState, AppDispatch } from '~/redux/store';

export const useAppAuth = () => {
  const auth = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch<AppDispatch>();
  const ref = useRef({dispatch});


  const login = useCallback(async (loginPayload: LoginPayload) => {
    try {
      return await ref.current.dispatch(doLogin(loginPayload)).unwrap();
    } catch (error: any) {
      if (import.meta.env.DEV) console.error('Login error:', error);
      return {message: 'Login error : ' + error.message};
    }
  }, []);

  const logout = useCallback(() => {
    try {
      ref.current.dispatch(doLogout());
    } catch (error) {
      if (import.meta.env.DEV) console.error('Logout error:', error);
    }
  }, []);

  const hasPermission = useCallback((permission: string) => {
    return auth.user?.role.toLowerCase() === 'admin'? true :
            auth.user?.permissions?.includes(permission) || false;
  }, [auth.user]);

  const hasRole = useCallback((role: string) => {
    return auth.user?.role === role;
  }, [auth.user]);

  const hasAnyPermission = useCallback((permissions: string[]) => {
    return auth.user?.role.toLowerCase() === 'admin'? true :
            !auth.user?.permissions? false :
              permissions.some(permission => auth.user!.permissions.includes(permission));
  }, [auth.user]);

  const permissions = useMemo(() => auth.user?.permissions || [], [auth.user]);
  const role = useMemo(() => auth.user?.role || '', [auth.user]);
  const isAuthenticated = useMemo(() => auth.isAuthenticated, [auth.isAuthenticated]);
  const user = useMemo(() => auth.user, [auth.user]);

  return useMemo(()=> ({
    login,
    logout,
    hasPermission,
    hasRole,
    hasAnyPermission,
    permissions,
    role,
    isAuthenticated,
    user
  }), [login,
    logout,
    hasPermission,
    hasRole,
    hasAnyPermission,
    permissions,
    role,
    isAuthenticated,
    user]);
};

export default useAppAuth;
