export interface Prime {
  id: string;
  name: string;
  type: PrimeType;
  amount: number;
  date: string;
  status: PrimeStatus;
  fileName?: string;
  periodicite?: Periodicite;
  avec_unite?: boolean;
  description?: string;
  periode?: string;
  unites?: UniteVersement[];
}

export enum PrimeType {
  ANNUAL = 'annual',
  PERFORMANCE = 'performance',
  PROJECT = 'project',
  EXCEPTIONAL = 'exceptional',
  OTHER = 'other'
}

export enum Periodicite {
  ANNUELLE = 'annuelle',
  TRIMESTRIELLE = 'trimestrielle',
  MENSUELLE = 'mensuelle'
}

export enum PrimeStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface UniteVersement {
  id: string;
  nom: string;
  montant: number;
}

export interface PrimeListResponse {
  data: Prime[];
  total: number;
}

export interface PrimeUploadResponse {
  success: boolean;
  data: Prime[];
}

export interface PrimeTypeMock {
  id: string;
  code: string;
  libelle: string;
  periodicite: Periodicite;
  description: string;
  avec_unite: boolean;
}
