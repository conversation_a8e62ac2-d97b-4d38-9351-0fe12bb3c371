import { ThemeMode } from 'antd-style';
import { THEMES } from '~/constants';

// PrimeReact theme configuration
export interface PrimeReactThemeConfig {
  colorScheme: 'light' | 'dark';
  primaryColor: string;
  fontFamily: string;
}

/**
 * Get PrimeReact theme configuration based on Ant Design theme
 * @param appearance - The current theme mode (light, dark, auto)
 * @returns PrimeReact theme configuration
 */
export const getPrimeReactThemeConfig = (appearance: ThemeMode = 'light'): PrimeReactThemeConfig => {
  const theme = THEMES[appearance];

  return {
    colorScheme: appearance === 'light' ? 'light' : 'dark',
    primaryColor: theme.primary,
    fontFamily: '"Lato", "Inter", ui-sans-serif, system-ui, sans-serif',
  };
};

/**
 * Generate CSS variables for PrimeReact theme
 * @param config - PrimeReact theme configuration
 * @returns CSS variables as a string
 */
export const generatePrimeReactThemeVariables = (config: PrimeReactThemeConfig): string => {
  const { colorScheme, primaryColor } = config;

  // Base colors
  const isLight = colorScheme === 'light';
  const surfaceColor = isLight ? '#ffffff' : '#141414';
  const textColor = isLight ? 'rgba(0, 0, 0, 0.88)' : 'rgba(255, 255, 255, 0.85)';
  const textColorSecondary = isLight ? 'rgba(0, 0, 0, 0.45)' : 'rgba(255, 255, 255, 0.45)';
  const surfaceHover = isLight ? '#f5f5f5' : '#303030';
  const borderColor = isLight ? '#d9d9d9' : '#424242';
  const inputBg = isLight ? '#ffffff' : '#000000';

  return `
    --primary-color: ${primaryColor};
    --primary-color-text: ${isLight ? '#ffffff' : '#000000'};
    --surface-a: ${surfaceColor};
    --surface-b: ${isLight ? '#f5f5f5' : '#1f1f1f'};
    --surface-c: ${isLight ? '#f0f0f0' : '#303030'};
    --surface-d: ${isLight ? '#d9d9d9' : '#424242'};
    --surface-e: ${surfaceColor};
    --surface-f: ${surfaceColor};
    --text-color: ${textColor};
    --text-color-secondary: ${textColorSecondary};
    --surface-hover: ${surfaceHover};
    --focus-ring: 0 0 0 2px ${primaryColor}20;
    --input-bg: ${inputBg};
    --input-border: ${borderColor};
    --input-border-hover: ${primaryColor};
    --input-border-focus: ${primaryColor};
    --input-placeholder: ${textColorSecondary};
  `;
};

/**
 * Apply PrimeReact theme to document
 * @param appearance - The current theme mode (light, dark, auto)
 */
export const applyPrimeReactTheme = (appearance: ThemeMode = 'light'): void => {
  // Handle 'auto' theme by checking system preference
  let effectiveAppearance = appearance;
  if (appearance === 'auto') {
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    effectiveAppearance = prefersDark ? 'dark' : 'light';
  }

  const config = getPrimeReactThemeConfig(effectiveAppearance);
  const cssVars = generatePrimeReactThemeVariables(config);

  // Apply CSS variables to :root
  document.documentElement.setAttribute('data-theme', config.colorScheme);

  // Also set color-scheme CSS property
  document.documentElement.style.colorScheme = config.colorScheme;

  // Create or update style element
  let styleEl = document.getElementById('prime-react-theme-vars');
  if (!styleEl) {
    styleEl = document.createElement('style');
    styleEl.id = 'prime-react-theme-vars';
    document.head.appendChild(styleEl);
  }

  styleEl.innerHTML = `:root {
    ${cssVars}
  }`;

  // Force update of any PrimeReact components
  document.dispatchEvent(new CustomEvent('primereact-theme-changed', {
    detail: { theme: config.colorScheme }
  }));
};
