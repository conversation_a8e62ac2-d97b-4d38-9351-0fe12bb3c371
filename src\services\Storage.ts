/* eslint-disable @typescript-eslint/no-unused-vars */
import { PREFIXCLS } from "~/constants";
import { Local, ThemeMode } from "~/models";

const PERSIST = `persist:${PREFIXCLS}`

export default class Storage {

  private static getData(where='localStorage'):string {
    if (where==='localStorage') return window.localStorage.getItem(PERSIST) ?? "{}"
    else return window.sessionStorage.getItem(PERSIST) ?? "{}"
  }

  static persist(state:object, ifNotExist=true, where='localStorage'): void {
    if (ifNotExist && window.localStorage.getItem(PERSIST)) return
    try {
      const data = JSON.parse(Storage.getData(where))
      Object.assign(data, state)
      if (where==='localStorage') window.localStorage.setItem(PERSIST, JSON.stringify(data))
      else window.sessionStorage.setItem(PERSIST, JSON.stringify(data))
    } catch (error) { /* empty */ }
  }

  static readToken(): string {
    try {
      let data = JSON.parse(Storage.getData('sessionStorage'))
      if (Object.keys(data).length === 0 || !data.token) data = JSON.parse(Storage.getData())
      return data.token ?? ""
    } catch (error) {
      return ""
    }
  }

  static persistToken(token:string, inLocal=false): void {
    try {
      let data
      if(inLocal) data = JSON.parse(Storage.getData())
      else data = JSON.parse(Storage.getData('sessionStorage'))
      data.token = token // add new token to data
      if(inLocal) window.localStorage.setItem(PERSIST, JSON.stringify(data))
      else window.sessionStorage.setItem(PERSIST, JSON.stringify(data))
    } catch (error) { /* empty */ }
  }

  static deleteToken(): void {
    let data
    try {
      // delete sessionStorage token
      data = JSON.parse(Storage.getData('sessionStorage'))
      if (Object.keys(data).length !== 0 && data.token) {
        delete data.token
        window.sessionStorage.setItem(PERSIST, JSON.stringify(data))
      }

      // delete localStorage token
      data = JSON.parse(Storage.getData())
      if (Object.keys(data).length !== 0 && data.token) {
        delete data.token
        window.localStorage.setItem(PERSIST, JSON.stringify(data))
      }
    } catch (error) { /* empty */ }
  }

  static readLocal(): Local {
    try {
      const data = JSON.parse(Storage.getData())
      return data.local ?? "fr"
    } catch (error) {
      return "fr"
    }
  }

  static persistLocal(local:Local): void {
    try {
      const data = JSON.parse(Storage.getData())
      data.local = local
      window.localStorage.setItem(PERSIST, JSON.stringify(data))
    } catch (error) { /* empty */ }
  }

  static readTheme(): ThemeMode {
    try {
      const data = JSON.parse(Storage.getData())
      return data.theme ?? "auto"
    } catch (error) {
      return "auto"
    }
  }

  static persistTheme(theme:ThemeMode): void {
    try {
      const data = JSON.parse(Storage.getData())
      data.theme = theme
      localStorage.setItem(PERSIST, JSON.stringify(data))
    } catch (error) { /* empty */ }
  }

}
