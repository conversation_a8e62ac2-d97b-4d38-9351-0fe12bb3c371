import { Suspense, useEffect } from "react"
import { useLocation } from "react-router"
import { Loading } from "~/components"
import { useAppAuth } from "~/hooks"
import AppError from "~/services/AppError"

// Create an HOC to protect your route components with roles
export const protectPage = (Component: any, props?: any) => {
  return () => {
    const { pathname } = useLocation()
    const { user } = useAppAuth();

    useEffect(() => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: "smooth",
      }) // Scroll to the top when the location changes
    }, [pathname])

    if (props) {
      const {requiredRoles, revokedRoles}: {requiredRoles: any[], revokedRoles: any[]} = props;

        // Vérifier les rôles si nécessaire
        if (requiredRoles && requiredRoles.length > 0) {
          const hasRequiredRole = requiredRoles.some((role:string) => role.toUpperCase() === (user?.role || ''));
          if (!hasRequiredRole) {
            // Rediriger vers une page d'erreur 403 si l'utilisateur n'a pas les droits
            throw new AppError("Accès non autorisé",undefined, 403);
          }
        }

        if (revokedRoles && revokedRoles.length > 0) {
          const hasRevokedRole = revokedRoles.some((role:string) => role.toUpperCase() === (user?.role || ''));
          if (hasRevokedRole) {
            // Rediriger vers une page d'erreur 403 si l'utilisateur n'a pas les droits
            throw new AppError("Accès non autorisé",undefined, 403);
          }
        }
    }

    return (
      <Suspense fallback={<Loading />}>
        <Component {...props} />
      </Suspense>
    )
  }
}
