import { Suspense, useEffect } from "react"
import { useLocation } from "react-router"
import { Loading } from "~/components"

// Create an HOC to wrap your route components with ScrollToTop
export const wrapPage = (Component: any, props?: any) => {
  return () => {
    const { pathname } = useLocation()

    useEffect(() => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: "smooth",
      }) // Scroll to the top when the location changes
    }, [pathname])

    return (
      <Suspense fallback={<Loading />}>
        <Component {...props} />
      </Suspense>
    )
  }
}
