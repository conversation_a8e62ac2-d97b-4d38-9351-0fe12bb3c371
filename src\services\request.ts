import { extend } from 'umi-request';
import { API1 } from '~/constants';
import AppError from './AppError';
import Storage from './Storage';

const prefix = !import.meta.env.VITE_USE_MIRAGE ? API1 : undefined

// Appel d'umi-request
const request = extend({
  prefix,
  timeout: 10000, // Temps maximum pour une requête
  headers: { 'Content-Type': 'application/json' },
  errorHandler: (error) => {
    if (import.meta.env.DEV) console.dir(error, {depth: null});
    throw new AppError(error.message, error.data?? error, error.response?.status);
  },
});

// Add request interceptor
request.interceptors.request.use((url, options) => {
  const token = Storage.readToken;
  const headers = options.headers as Record<string, string>;

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  return {
    url,
    options: { ...options, headers },
  };
});

export default request;
