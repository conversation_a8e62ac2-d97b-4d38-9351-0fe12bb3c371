import { ArrowRightOutlined, LinkOutlined } from '@ant-design/icons';
import { Avatar, Button, Card, Tag } from 'antd';
import React from 'react';
import { Link } from 'react-router';
import { Service } from '~/services';
import { createStyles } from '~/themes';

interface ServiceCardProps {
  service: Service;
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
}) => {
  const { styles } = useStyles();

  const getCategoryColor = () => {
    switch (service.category) {
      case 'public': return 'green';
      case 'private': return 'red';
      default: return 'default';
    }
  };

  const getCategoryLabel = () => {
    switch (service.category) {
      case 'public': return 'Public';
      case 'private': return 'Privé';
      default: return '';
    }
  };

  const renderCardContent = () => (
    <Card
      className={`${styles.serviceCard}`}
      // hoverable={canAccess}
      cover={
        service.imageUrl && (
          <div className={styles.imageContainer}>
            <img
              alt={service.title}
              src={service.imageUrl}
              className={styles.serviceImage}
              onError={(e) => {
                e.currentTarget.src = '/assets/douaneBeninLogo.png'; // Image par défaut
              }}
            />
          </div>
        )
      }
      actions={[
        service.isExternal ? (
            <Button
              type="primary"
              icon={<LinkOutlined />}
              className={styles.actionButton}
            >
              Ouvrir
            </Button>
          ) : (
            <Button
              type="primary"
              icon={<ArrowRightOutlined />}
              className={styles.actionButton}
            >
              Accéder
            </Button>
          )
      ]}
    >
      <Card.Meta
        avatar={
          [
            <Avatar
              size="large"
              style={{ backgroundColor: getCategoryColor() === 'green' ? '#52c41a' : '#ff4d4f' }}
              className='font-bold'
            >
              {service.title.charAt(0).toUpperCase() + service.title.split(' ')?.[1].charAt(0).toUpperCase()}
            </Avatar>,
            <Tag color={getCategoryColor()} className={styles.categoryTag}>
              {getCategoryLabel()}
            </Tag>
          ]
        }
        title={
          <div className={styles.titleContainer}>
            <span className={styles.serviceTitle}>{service.title}</span>
          </div>
        }
        description={
          <div className={styles.descriptionContainer}>
            <p className={styles.serviceDescription}>{service.description}</p>
          </div>
        }
      />
    </Card>
  );

  if (service.isExternal) {
    return (
      <a href={service.route} target="_blank" rel="noopener noreferrer">
        {renderCardContent()}
      </a>
    );
  }

  return (
    <Link to={service.route}>
      {renderCardContent()}
    </Link>
  );
};

const useStyles = createStyles(({ token }) => ({
  serviceCard: {
    height: '100%',
    borderRadius: '12px',
    // border: `1px solid ${token.colorBorder}`,
    transition: 'all 0.3s ease',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
      // borderColor: token.colorPrimary,
    },
  },
  imageContainer: {
    height: '120px',
    overflow: 'hidden',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: token.colorBgContainer,
  },
  serviceImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
  },
  titleContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    gap: '8px',
  },
  serviceTitle: {
    fontSize: '16px',
    fontWeight: 600,
    color: token.colorText,
    lineHeight: '1.4',
    flex: 1,
  },
  categoryTag: {
    display: 'block',
    margin: '10px 0 0 -5px',
    // marginTop: '7px',
    fontSize: '12px',
    flexShrink: 0,
  },
  descriptionContainer: {
    marginTop: '8px',
  },
  serviceDescription: {
    color: token.colorTextSecondary,
    fontSize: '14px',
    lineHeight: '1.5',
    margin: 0,
    marginBottom: '8px',
  },
  accessInfo: {
    color: token.colorTextTertiary,
    fontSize: '12px',
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
  },
  rolesInfo: {
    fontStyle: 'italic',
  },
  actionButton: {
    borderRadius: '6px',
  },
}));

export default ServiceCard;
