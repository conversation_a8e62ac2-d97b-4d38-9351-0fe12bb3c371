import { createServer, Model, Response, RestSerializer } from 'miragejs';
import { Periodicite, PrimeStatus, PrimeType } from '~/models/Prime';
import IdentityManager from '~/services/IdentityManager';
import { createAuthMock } from './factories';
import fixtures from './fixtures';

// Fonction pour configurer et démarrer le serveur Mirage JS
export function startMirageServer(environment:string = "development") {
  const options = {
    environment,

    // factories,
    // fixtures,

    // Pour personnaliser la réponse
    serializers: {
      application: RestSerializer,
    },

    // Pour personnaliser les identifiants
    identityManagers: {
      user: IdentityManager,
    },
  }

  createServer({
    ...options, // Pour ajouter les options précédentes

    // Définition d'uns modèles
    models: {
      user: Model,
      country: Model,
      prime: Model,
      agent: Model,
    },

    // Données de test à l'initialisation du serveur
    seeds(server) {
      server.loadFixtures()

      server.createList("user", 5)
    },

    // Routes pour l'API simulée
    routes() {
      this.post("/signin", (schema, request) => {
        try {
          const { email, password } = JSON.parse(request.requestBody);
          return createAuthMock(email, password);
        } catch (error: any) {
          return new Response(401, {}, {
            message: error.message || 'Authentication failed'
          });
        }
      });

      // Responding to a POST request
      this.post("/old/movies", (schema, request) => {
        const attrs = JSON.parse(request.requestBody)
        attrs.id = Math.floor(Math.random() * 100)

        return { movie: attrs }
      })

      // Using the `timing` option to slow down the response
      this.get(
        "/old/movies",
        () => {
          return {
            movies: [
              { id: 1, name: "Inception", year: 2010 },
              { id: 2, name: "Interstellar", year: 2014 },
              { id: 3, name: "Dunkirk", year: 2017 },
            ],
          }
        },
        { timing: 4000 }
      )

      // Using the `Response` class to return a 500
      this.delete("/old/movies/1", () => {
        const headers = {}
        const data = { errors: ["Server did not respond"] }

        return new Response(500, headers, data)
      })


      // API pour les primes
      this.get("/primes/types", () => {
        return fixtures.primeTypes;
      });

      this.get("/primes", () => {
        return {
          data: [
            {
              id: IdentityManager.fetchUUID(),
              name: "Prime de fin d'année 2023",
              type: PrimeType.ANNUAL,
              amount: 500000,
              date: new Date().toISOString(),
              status: PrimeStatus.APPROVED,
              periodicite: Periodicite.ANNUELLE,
              avec_unite: true,
              description: "Prime annuelle pour tous les agents",
              periode: "2023"
            },
            {
              id: IdentityManager.fetchUUID(),
              name: "Prime de performance T2 2023",
              type: PrimeType.PERFORMANCE,
              amount: 250000,
              date: new Date().toISOString(),
              status: PrimeStatus.PENDING,
              periodicite: Periodicite.TRIMESTRIELLE,
              avec_unite: true,
              description: "Prime trimestrielle basée sur les performances",
              periode: "T2"
            }
          ],
          total: 2
        };
      });

      this.get("/primes/:id", (schema, request) => {
        const id = request.params.id;
        // Simuler la récupération d'une prime spécifique
        return {
          id,
          name: "Prime de fin d'année 2023",
          type: PrimeType.ANNUAL,
          amount: 500000,
          date: new Date().toISOString(),
          status: PrimeStatus.APPROVED,
          periodicite: Periodicite.ANNUELLE,
          avec_unite: true,
          description: "Prime annuelle pour tous les agents",
          periode: "2023"
        };
      });

      this.post("/primes", (schema, request) => {
        console.log("request", request.requestBody);
        const attrs = JSON.parse(request.requestBody);
        console.log("attrs", attrs);

        attrs.id = IdentityManager.fetchUUID();
        return attrs;
      });

      this.put("/primes/:id", (schema, request) => {
        const id = request.params.id;
        const attrs = JSON.parse(request.requestBody).data;
        return { ...attrs, id };
      });

      this.delete("/primes/:id", () => {
        return new Response(204);
      });

      this.post("/primes/upload", () => {
        return {
          success: true,
          data: [
            {
              id: IdentityManager.fetchUUID(),
              name: "Agent 1",
              type: PrimeType.ANNUAL,
              amount: 50000,
              date: new Date().toISOString(),
              status: PrimeStatus.DRAFT,
              fileName: "import.xlsx"
            },
            {
              id: IdentityManager.fetchUUID(),
              name: "Agent 2",
              type: PrimeType.ANNUAL,
              amount: 45000,
              date: new Date().toISOString(),
              status: PrimeStatus.DRAFT,
              fileName: "import.xlsx"
            }
          ]
        };
      });

      // API pour les agents
      this.get("/agents", () => {
        return {
          data: fixtures.agents,
          total: fixtures.agents.length
        };
      });

      this.post("/agents/repartitions", () => {
        return new Response(204);
      });

      this.resource('user')
      this.resource('country', {path: "cities"})

      this.passthrough()
      // this.passthrough("https://jsonplaceholder.typicode.com/**")
    },
  });
}
