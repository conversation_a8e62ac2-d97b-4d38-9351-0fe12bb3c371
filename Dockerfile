# Build step
FROM node:latest AS build

# Installer pnpm globalement
RUN npm install -g pnpm

WORKDIR /app

# Copier les fichiers de configuration pnpm et package.json
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

# Installer les dépendances avec pnpm
RUN pnpm install --frozen-lockfile

# Copier le reste du code source
COPY . .

# Construire l'application
RUN pnpm run build

# Serveur static
FROM nginx:latest
COPY --from=build /app/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
