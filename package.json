{"name": "erp-frontend-with-vite-react-swc-dgd", "version": "2.1.0", "type": "module", "license": "MIT", "private": true, "scripts": {"dev": "vite", "preview": "vite preview", "build": "vite build", "build:check": "tsc -b && vite build", "lint": "eslint .", "test": "vitest", "format": "prettier --write ."}, "dependencies": {"@ant-design/colors": "^7.1.0", "@ant-design/icons": "^5.5.2", "@ant-design/pro-components": "^2.8.7", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/css": "^11.13.5", "@reduxjs/toolkit": "^2.5.0", "@tanem/react-nprogress": "^5.0.53", "ahooks": "^3.8.4", "antd": "^5.22.6", "antd-style": "^3.7.1", "dayjs": "^1.11.13", "i18next": "^24.2.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "primeicons": "^7.0.0", "primereact": "^10.9.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-i18next": "^15.2.0", "react-redux": "^9.2.0", "react-responsive": "^10.0.0", "react-router": "^7.6.1", "react-transition-group": "^4.4.5", "umi-request": "^1.4.0", "uuid": "^11.0.5", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@faker-js/faker": "^9.7.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.13", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-transition-group": "^4.4.12", "@uiw/react-json-view": "2.0.0-alpha.30", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "faker": "^6.6.6", "globals": "^15.14.0", "jsonwebtoken": "^9.0.2", "miragejs": "^0.1.48", "postcss": "^8.4.49", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.13", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5", "vite-tsconfig-paths": "^5.1.4"}}