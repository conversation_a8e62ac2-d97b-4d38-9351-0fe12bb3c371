/* Custom styles for search field */
.search-field {
  margin-left: 0 !important;
  width: 300px !important;
}

/* Fix for AutoComplete component */
.ant-select-auto-complete {
  width: 300px !important;
}

.ant-select-selector,
.ant-select-selection-search,
.ant-select-selection-search-input,
.ant-input-affix-wrapper,
.ant-input {
  background-color: var(--surface-a) !important;
  color: var(--text-color) !important;
  border-color: var(--input-border) !important;
  width: 100% !important;
}

/* Fix for search field when clicked/focused */
.ant-select-focused .ant-select-selector,
.ant-select-selector:focus,
.ant-select-selector:active,
.ant-select-open .ant-select-selector,
.ant-input-affix-wrapper-focused,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper:active,
.ant-input:focus,
.ant-input:active {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(7, 110, 229, 0.1) !important;
}

/* Fix for dropdown */
.ant-select-dropdown {
  background-color: var(--surface-a) !important;
  border-radius: 4px !important;
  border: 1px solid var(--input-border) !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
}

.ant-select-item {
  background-color: var(--surface-a) !important;
  color: var(--text-color) !important;
}

.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: var(--surface-b) !important;
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: var(--surface-c) !important;
  font-weight: 600;
}
