import { createSlice, Slice } from "@reduxjs/toolkit"
import { PATH_ADMIN } from "~/constants"
import { Menus } from "~/menus"

export interface TagState {
  activeTagId: string
  tags: Menus[]
  menus: Menus[]
}
const initialState: TagState = {
  activeTagId: location.pathname,
  tags: [],
  menus: [],
}

const tagSlice: Slice<TagState> = createSlice({
  name: "tag",
  initialState,
  reducers: {
    updateTagStore(state, action) {
      Object.assign(state, action.payload)
    },
    setActiveTagByPath(state, action) {
      state.activeTagId = action.payload
    },
    removeTagByPath(state, action) {
      const targetKey = action.payload
      let lastIndex = 0
      // state.tags.forEach((tag, i) => {
      //   if (tag.path === targetKey) {
      //     // state.tags.splice(i, 1)
      //     lastIndex = i - 1
      //   }
      // })
      const tagList = state.tags.filter((tag, i)=> {
        if (tag.path === targetKey) lastIndex = i - 1
        return tag.path !== targetKey
      })

      if (tagList.length && state.activeTagId === targetKey) {
        const newIndex = lastIndex >= 0 ? lastIndex : 0
        state.activeTagId = tagList[newIndex].path as string
      }

      if (tagList.length === 0) {
        state.activeTagId = PATH_ADMIN.root
      }
    },
    removeAllTag(state) {
      state.activeTagId = PATH_ADMIN.root
      state.tags = []
    },
    removeOtherTag(state) {
      const activeTag = state.tags.find(tag => tag.path === state.activeTagId)
      state.tags = [activeTag] as Menus[]
    },
    addTag(state, action) {
      if (!state.tags.find(tag => tag.path === action.payload.path)) {
        state.tags.push(action.payload)
      }
      state.activeTagId = action.payload.path
    },
    addMenu(state, action) {
      state.menus.push(action.payload)
    },
  },
})

export const { updateTagStore, setActiveTagByPath, removeTagByPath, removeAllTag, removeOtherTag, addTag, addMenu } =
  tagSlice.actions

export default tagSlice
