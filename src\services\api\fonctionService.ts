import { Fonction, CoefficientConfig } from '../../models/Agent';
import { PrimeType } from '../../models/Prime';
import { fonctions, coefficientsConfig } from '../../mocks/fixtures/fonctions';

export const getFonctions = async (): Promise<Fonction[]> => {
  try {
    // Dans un environnement réel, on ferait un appel API
    // const response = await request.get('/api/fonctions');
    // return response.data;

    // Pour le moment, on utilise les données mockées
    return Promise.resolve(fonctions);
  } catch (error) {
    console.error('Error fetching fonctions:', error);
    throw error;
  }
};

export const getCoefficientByFonctionAndPrime = async (
  fonctionCode: string,
  primeType: PrimeType
): Promise<number | null> => {
  try {
    // Dans un environnement réel, on ferait un appel API
    // const response = await request.get(`/api/coefficients/${fonctionCode}/${primeType}`);
    // return response.data.coefficient;

    // Pour le moment, on utilise les données mockées
    const config = coefficientsConfig.find(
      c => c.fonction === fonctionCode && c.primeType === primeType
    );
    return Promise.resolve(config?.coefficient || null);
  } catch (error) {
    console.error('Error fetching coefficient:', error);
    throw error;
  }
};

export const updateCoefficient = async (
  fonctionCode: string,
  primeType: PrimeType,
  coefficient: number
): Promise<void> => {
  try {
    // Dans un environnement réel, on ferait un appel API
    // await request.put(`/api/coefficients/${fonctionCode}/${primeType}`, { coefficient });

    // Pour le moment, on simule la mise à jour
    const index = coefficientsConfig.findIndex(
      c => c.fonction === fonctionCode && c.primeType === primeType
    );

    if (index !== -1) {
      coefficientsConfig[index].coefficient = coefficient;
    } else {
      // Créer un nouveau coefficient s'il n'existe pas
      coefficientsConfig.push({
        fonction: fonctionCode,
        primeType,
        coefficient
      });
    }

    return Promise.resolve();
  } catch (error) {
    console.error('Error updating coefficient:', error);
    throw error;
  }
};

export const getCoefficientByFonctionAndPrimeType = (fonction: string, primeType: PrimeType): number => {
  const config = coefficientsConfig.find(
    c => c.fonction === fonction && c.primeType === primeType
  );
  return config?.coefficient || 1.0;
};

export const getAllCoefficientsConfig = (): CoefficientConfig[] => {
  return coefficientsConfig;
};
