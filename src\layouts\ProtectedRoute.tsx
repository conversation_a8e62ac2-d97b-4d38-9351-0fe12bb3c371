import { Navigate, useLocation } from 'react-router';
import { PATH_AUTH, PATH_ERROR } from '~/constants';
import { useAppAuth } from '~/hooks';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  revokedRoles?: string[];
}

const ProtectedRoute = ({ 
  children, 
  requiredRoles, 
  revokedRoles 
}: ProtectedRouteProps) => {
  const { user } = useAppAuth();
  // const location = useLocation();

  // if (!isAuthenticated) {
  //   // Rediriger vers la page de connexion avec l'URL de retour
  //   return <Navigate to={PATH_AUTH.signin} state={{ from: location }} />;
  // }

  // Vérifier les rôles si nécessaire
  if (requiredRoles && requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.includes(user?.role || '');
    if (!hasRequiredRole) {
      // Rediriger vers une page d'erreur 403 si l'utilisateur n'a pas les droits
      return <Navigate to={PATH_ERROR.error403} />;
    }
  }

  if (revokedRoles && revokedRoles.length > 0) {
    const hasRevokedRole = revokedRoles.includes(user?.role || '');
    if (hasRevokedRole) {
      // Rediriger vers une page d'erreur 403 si l'utilisateur n'a pas les droits
      return <Navigate to={PATH_ERROR.error403} />;
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute; 