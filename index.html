<!doctype html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Métadonnées de base -->
    <meta name="title" content="%VITE_APP_TITLE%" />
    <meta name="description" content="%VITE_APP_DESCRIPTION%" />
    <meta name="author" content="%VITE_APP_AUTHOR%" />
    <meta name="theme-color" content="#1f1f1f" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="%VITE_APP_TITLE%" />
    <meta property="og:description" content="%VITE_APP_DESCRIPTION%" />
    <meta property="og:locale" content="fr_FR" />


    <!-- PWA -->
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.svg" />

    <!-- Sécurité -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;" />
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />

    <title>%VITE_APP_TITLE%</title>

    <!-- Style -->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        color-scheme: light dark;
      }

      body {
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      noscript {
        display: block;
        position: absolute;
        top: 0;
        padding: 10px;
        width: 100%;
        font-size: large;
        font-weight: bold;
        text-align: center;
        color: white;
        background-color: red;
      }

      .loader {
        display: inline-block;
        position: absolute;
        width: 40px;
        height: 40px;
        left: 0;
        right: 0;
        top: calc(50% - 20px);
        margin: auto;
      }

      .loader img {
        position: absolute;
        height: 25px;
        width: auto;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
      }

      .loader div {
        box-sizing: border-box;
        display: block;
        position: absolute;
        width: 40px;
        height: 40px;
        margin: 0;
        border: 3.4px solid #1976d2;
        border-radius: 50%;
        animation: loader 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
        border-color: #1976d2 transparent transparent transparent;
      }

      .loader div:nth-child(1) {
        animation-delay: -0.45s;
      }

      .loader div:nth-child(2) {
        animation-delay: -0.3s;
      }

      .loader div:nth-child(3) {
        animation-delay: -0.15s;
      }

      @keyframes loader {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <noscript>
      You must activate Javascript before using the app.
      <br />
      Vous devez activer JavaScript pour utiliser cette application.
    </noscript>
    <div id="root">
      <div class="loader">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>
    <script type="module" src="/src/index.ts"></script>
  </body>
</html>
