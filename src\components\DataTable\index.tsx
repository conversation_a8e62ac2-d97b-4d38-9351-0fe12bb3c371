import React, { useState, useEffect } from 'react';
import { DataTable as PrimeDataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { FilterMatchMode } from 'primereact/api';
import { createStyles } from '~/themes';

export interface DataTableColumn {
  field: string;
  header: string;
  sortable?: boolean;
  filter?: boolean;
  body?: (rowData: any) => React.ReactNode;
  style?: React.CSSProperties;
}

interface DataTableProps {
  data: any[];
  columns: DataTableColumn[];
  paginator?: boolean;
  rows?: number;
  rowsPerPageOptions?: number[];
  globalFilterFields?: string[];
  globalFilter?: string;
  onGlobalFilterChange?: (value: string) => void;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  emptyMessage?: string;
  className?: string;
  style?: React.CSSProperties;
  tableStyle?: React.CSSProperties;
  loading?: boolean;
  selectionMode?: 'single' | 'multiple' | 'checkbox' | null;
  onSelectionChange?: (e: any) => void;
  selection?: any | any[];
  onRowClick?: (e: any) => void;
  stripedRows?: boolean;
  size?: 'small' | 'normal' | 'large';
}

const DataTable: React.FC<DataTableProps> = ({
  data,
  columns,
  paginator = true,
  rows = 10,
  rowsPerPageOptions = [5, 10, 25, 50],
  globalFilterFields,
  globalFilter,
  onGlobalFilterChange,
  header,
  footer,
  emptyMessage = 'No records found',
  className = '',
  style,
  tableStyle,
  loading = false,
  selectionMode = null,
  onSelectionChange,
  selection,
  onRowClick,
  stripedRows = true,
  size = 'normal',
}) => {
  const { styles: tableStyles } = useStyles();
  const [filters, setFilters] = useState({
    global: { value: null as any, matchMode: FilterMatchMode.CONTAINS },
  });
  const [globalFilterValue, setGlobalFilterValue] = useState(globalFilter || '');

  // Update filters when globalFilter prop changes
  useEffect(() => {
    if (globalFilter !== undefined) {
      setGlobalFilterValue(globalFilter);
      setFilters(prev => ({
        ...prev,
        global: { ...prev.global, value: globalFilter || null }
      }));
    }
  }, [globalFilter]);

  const handleGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const _filters = { ...filters };

    _filters['global'].value = value;

    setFilters(_filters);
    setGlobalFilterValue(value);

    if (onGlobalFilterChange) {
      onGlobalFilterChange(value);
    }
  };

  const renderHeader = () => {
    if (!header && !globalFilterFields) return null;

    return (
      <div className="flex justify-content-between align-items-center">
        {header}

        {globalFilterFields && (
          <span className="p-input-icon-left">
            <i className="pi pi-search" />
            <InputText
              value={globalFilterValue}
              onChange={handleGlobalFilterChange}
              placeholder="Search..."
              className="p-inputtext-sm"
            />
          </span>
        )}
      </div>
    );
  };

  const getSizeClass = () => {
    switch (size) {
      case 'small':
        return 'p-datatable-sm';
      case 'large':
        return 'p-datatable-lg';
      default:
        return '';
    }
  };

  return (
    <div className={tableStyles.tableWrapper}>
      <PrimeDataTable
        value={data}
        paginator={paginator}
        rows={rows}
        rowsPerPageOptions={rowsPerPageOptions}
        filters={filters}
        globalFilterFields={globalFilterFields}
        header={renderHeader()}
        footer={footer}
        emptyMessage={emptyMessage}
        className={`${className} ${getSizeClass()} ${stripedRows ? 'p-datatable-striped' : ''}`}
        style={style}
        loading={loading}
        selectionMode={selectionMode as any}
        selection={selection}
        onSelectionChange={onSelectionChange}
        onRowClick={onRowClick}
        pt={{ table: { style: tableStyle } }}
        scrollable
        scrollHeight="flex"
      >
        {columns.map((col) => (
          <Column
            key={col.field}
            field={col.field}
            header={col.header}
            sortable={col.sortable !== false}
            filter={col.filter}
            body={col.body}
            style={col.style}
          />
        ))}
      </PrimeDataTable>
    </div>
  );
};

const useStyles = createStyles(({ token, css }) => {
  return {
    tableWrapper: css`
      .p-datatable {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .p-datatable-header {
          background-color: ${token.colorBgContainer};
          border-bottom: 1px solid ${token.colorBorderSecondary};
          padding: 1rem;
        }

        .p-datatable-thead > tr > th {
          background-color: ${token.colorBgContainer};
          color: ${token.colorTextHeading};
          font-weight: 600;
          padding: 0.75rem 1rem;
          border-color: ${token.colorBorderSecondary};
        }

        .p-datatable-tbody > tr {
          background-color: ${token.colorBgContainer};

          &:hover {
            background-color: ${token.colorBgTextHover};
          }

          > td {
            padding: 0.75rem 1rem;
            border-color: ${token.colorBorderSecondary};
          }
        }

        .p-paginator {
          background-color: ${token.colorBgContainer};
          border-top: 1px solid ${token.colorBorderSecondary};
          padding: 0.5rem;
        }

        &.p-datatable-striped {
          .p-datatable-tbody > tr:nth-child(even) {
            background-color: ${token.colorFillAlter || 'rgba(0, 0, 0, 0.02)'};
          }
        }
      }
    `,
  };
});

export default DataTable;
