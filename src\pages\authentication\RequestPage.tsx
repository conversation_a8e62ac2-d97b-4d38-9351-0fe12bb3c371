import { CheckCircleOutlined, DownOutlined, HomeOutlined, LoginOutlined, MailOutlined } from "@ant-design/icons"
import { ProCard, ProFormText } from "@ant-design/pro-components"
import { Alert, Button, Dropdown, Flex, Form, Image, Result, Space, Typography } from "antd"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Link } from "react-router"
import EnUSSvg from "~/assets/en_US.svg?url"
import FrFRSvg from "~/assets/fr_FR.svg"
import { LogoDouane } from "~/components"
import { PATH_AUTH, PATH_GUEST } from "~/constants"
import { useAppLocal, useAppTheme } from "~/hooks"
import { themeIcon } from "~/services/utils"
import { createStyles } from "~/themes"

const { Title, Text } = Typography

interface RequestFormData {
  email: string;
}

const initialValues: RequestFormData = {
  email: "",
}

const RequestPage = () => {
  const { theme, updateTheme } = useAppTheme()
  const { local, setLocal } = useAppLocal()
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [emailSent, setEmailSent] = useState(false)
  const [submittedEmail, setSubmittedEmail] = useState("")
  const { styles } = useStyles()

  const onFinish = async (values: RequestFormData) => {
    setLoading(true)
    try {
      // Simuler l'envoi d'email (dans un vrai projet, appel API ici)
      await new Promise(resolve => setTimeout(resolve, 2000))

      setSubmittedEmail(values.email)
      setEmailSent(true)
    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'email:', error)
    } finally {
      setLoading(false)
    }
  }

  const toggleTheme = () => {
    const newTheme = theme === "dark" ? "light" : "dark";
    updateTheme(newTheme);
  }



  // Affichage de la confirmation d'envoi d'email
  if (emailSent) {
    return (
      <ProCard layout="center" className="min-h-screen">
        <Flex vertical align="center" justify="center" className="text-center my-12">
          <div className="flex justify-between w-full mb-2">
            <Button
              shape="round"
              color="default"
              variant="filled"
              icon={themeIcon(theme)}
              onClick={() => toggleTheme()}
            />
            <Dropdown
              menu={{
                onClick: (info: any) => setLocal(info.key),
                items: [
                  {
                    key: "en",
                    icon: <Image preview={false} src={EnUSSvg} alt="en" />,
                    disabled: local === "en",
                    label: <span className="ml-1">{t("English")}</span>,
                  },
                  {
                    key: "fr",
                    icon: <Image preview={false} src={FrFRSvg} alt="fr" />,
                    disabled: local === "fr",
                    label: <span className="ml-1">{t("French")}</span>,
                  },
                ],
              }}
            >
              <Button
                shape="round"
                color="default"
                variant="filled"
                className="font-bold"
                onClick={e => e.preventDefault()}
              >
                <Space>
                  {local}
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
          </div>

          <Result
            icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            title="Email envoyé avec succès !"
            subTitle={
              <div className={styles.successMessage}>
                <Text>
                  Un email contenant vos informations d'authentification a été envoyé à :
                </Text>
                <Text strong className={styles.emailAddress}>
                  {submittedEmail}
                </Text>
                <Text type="secondary" className={styles.instructionText}>
                  Veuillez vérifier votre boîte de réception.
                </Text>
              </div>
            }
            extra={[
              <Link to={PATH_GUEST.root} key="home">
                <Button
                  type="default"
                  icon={<HomeOutlined />}
                  size="large"
                  className={styles.actionButton}
                >
                  Retour à l'accueil
                </Button>
              </Link>,
              <Link to={PATH_AUTH.signin} key="signin">
                <Button
                  type="primary"
                  icon={<LoginOutlined />}
                  size="large"
                  className={styles.actionButton}
                >
                  Page de connexion
                </Button>
              </Link>
            ]}
          />

          <footer className="text-center font-bold mt-8">{t("global.footer")}</footer>
        </Flex>
      </ProCard>
    )
  }

  return (
    <ProCard layout="center" className="min-h-screen">
      <Flex vertical align="center" justify="center" className={"text-center my-12"}>
        <div className="flex justify-between w-full mb-2">
          <Button
            shape="round"
            color="default"
            variant="filled"
            icon={themeIcon(theme)}
            onClick={() => toggleTheme()}
          />
          <Dropdown
            menu={{
              onClick: (info: any) => setLocal(info.key),
              items: [
                {
                  key: "en",
                  icon: <Image preview={false} src={EnUSSvg} alt="en" />,
                  disabled: local === "en",
                  label: <span className="ml-1">{t("English")}</span>,
                },
                {
                  key: "fr",
                  icon: <Image preview={false} src={FrFRSvg} alt="fr" />,
                  disabled: local === "fr",
                  label: <span className="ml-1">{t("French")}</span>,
                },
              ],
            }}
          >
            <Button
              shape="round"
              color="default"
              variant="filled"
              className="font-bold"
              onClick={e => e.preventDefault()}
            >
              <Space>
                {local}
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </div>
        <LogoDouane />
        <Title level={3} className="my-5 uppercase">
          {t("Demande de compte / Réinitialisation")}
        </Title>

        <Alert
          message="Information"
          description="Saisissez votre adresse email pour recevoir vos informations d'authentification ou créer un nouveau compte."
          type="info"
          showIcon
          className={styles.infoAlert}
        />
        <Form<RequestFormData>
          onFinish={onFinish}
          className="w-[350px]"
          initialValues={initialValues}
        >
          <ProFormText
            name="email"
            fieldProps={{
              size: "large",
              prefix: <MailOutlined className="prefixIcon" />,
            }}
            placeholder="<EMAIL>"
            rules={[
              {
                required: true,
                message: t("L'adresse email est requise"),
              },
              {
                type: "email",
                message: t("Veuillez saisir une adresse email valide"),
              },
            ]}
          />

          <Form.Item className="mt-4">
            <Button
              size="large"
              htmlType="submit"
              type="primary"
              className="w-full"
              loading={loading}
              icon={<MailOutlined />}
            >
              {loading ? t("Envoi en cours...") : t("Envoyer la demande")}
            </Button>
          </Form.Item>
        </Form>

        <div className="mb-4 mt-6">
          <Text type="secondary">
            {t("Vous avez déjà un compte ?")}
          </Text>
          <br />
          <Link to={PATH_AUTH.signin} className={styles.linkButton}>
            {t("Retour à la page de connexion")}
          </Link>
        </div>

        <footer className="text-center font-bold">{t("global.footer")}</footer>
      </Flex>
    </ProCard>
  )
}

const useStyles = createStyles(({ token }) => ({
  successMessage: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    textAlign: 'center',
  },
  emailAddress: {
    fontSize: '16px',
    color: token.colorPrimary,
    padding: '8px 16px',
    backgroundColor: token.colorPrimaryBg,
    borderRadius: '6px',
    margin: '8px 0',
  },
  instructionText: {
    fontSize: '14px',
    marginTop: '8px',
  },
  actionButton: {
    minWidth: '160px',
    margin: '0 8px',
  },
  infoAlert: {
    marginBottom: '24px',
    maxWidth: '400px',
    textAlign: 'left',
  },
  linkButton: {
    color: token.colorPrimary,
    fontWeight: 500,
    '&:hover': {
      color: token.colorPrimaryHover,
    },
  },
}))

export default RequestPage
