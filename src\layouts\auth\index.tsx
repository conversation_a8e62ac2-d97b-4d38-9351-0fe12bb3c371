import React from "react";
import { Navigate, Outlet } from "react-router";
import { useAppAuth } from "~/hooks";
import { PATH_GUEST } from "~/constants";

export const AuthLayout = ({children}: {children?: React.ReactNode}) => {
    const { isAuthenticated } = useAppAuth();
    if (isAuthenticated) {
      // window.location.replace(PATH_GUEST.root)
      return <Navigate to={PATH_GUEST.root} replace />;
    }

    return children ?? <Outlet />;
};
