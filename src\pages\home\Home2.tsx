import { Image, Layout } from "antd"
import { useTranslation } from "react-i18next"
import { createStyles } from "~/themes"

const { Footer } = Layout
export default function Home2({ withFooter }: Readonly<{ withFooter?: boolean }>) {
  const { styles } = useStyles()
  const { t } = useTranslation()

  return (
    <main className={styles.main}>
      <div className={styles.container}>
        <Image preview={false} src="/assets/douaneBeninLogo.png" alt="Logo douane" />
        <Image preview={false} src="/assets/mefOfficialLogo.png" alt="Logo mef" width={200} className="mt-8" />
        <Footer className={withFooter ? "text-center text-white font-bold bg-transparent absolute bottom-0" : "hidden"}>
          {t("global.footer")}
        </Footer>
      </div>
    </main>
  )
}

const useStyles = createStyles(({ token }) => {
  return {
    // Balise main avec margin et padding à zéro
    main: {
      margin: 0,
      padding: 0,
      width: "100%",
      height: "100%",
    },
    // Supports the writing style of css object
    container: {
      backgroundColor: token.colorBgLayout,
      backgroundImage: 'url("/assets/homeBackground.png")',
      backgroundPosition: "center",
      backgroundSize: "cover",
      backgroundAttachment: "fixed",
      width: "100vw",
      height: "100vh",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      flexDirection: "column",
      margin: 0,
      padding: 0,
      overflow: "hidden",
      position: "fixed",
      top: 0,
      left: 0,
      zIndex: 1,
    },
  }
})
