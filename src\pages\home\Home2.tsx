import { Image, Layout } from "antd"
import { useTranslation } from "react-i18next"
import { createStyles } from "~/themes"

const { Footer } = Layout
export default function Home2({ withFooter }: Readonly<{ withFooter?: boolean }>) {
  const { styles } = useStyles()
  const { t } = useTranslation()

  return (
    <div className={styles.container}>
      <Image preview={false} src="/assets/douaneBeninLogo.png" alt="Logo douane" />
      <Image preview={false} src="/assets/mefOfficialLogo.png" alt="Logo mef" width={200} className="mt-8" />
      <Footer className={withFooter ? "text-center text-white font-bold bg-transparent absolute bottom-0" : "hidden"}>
        {t("global.footer")}
      </Footer>
    </div>
  )
}

const useStyles = createStyles(({ token }) => {
  return {
    // Supports the writing style of css object
    container: {
      backgroundColor: token.colorBgLayout,
      backgroundImage: 'url("/assets/homeBackground.png")',
      backgroundPosition: "center",
      backgroundSize: "cover",
      width: "100%",
      height: "calc(100vh - 150px)",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      flexDirection: "column",
    },
  }
})
